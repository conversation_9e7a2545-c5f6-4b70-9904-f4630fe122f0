using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Addresses
{
    public class DeleteModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DeleteModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int AddressId { get; set; }

        public string AddressDisplay { get; set; } = string.Empty;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var address = await _context.Addresses
                .Where(a => a.Id == id && a.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (address == null)
            {
                return NotFound();
            }

            AddressId = address.Id;
            AddressDisplay = $"{address.HouseFlatNumber} {address.StreetLineOne}, {address.City}, {address.Postcode}";

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var address = await _context.Addresses
                .Where(a => a.Id == id && a.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (address == null)
            {
                return NotFound();
            }

            // Handle cascade delete logic according to requirements
            await HandleCascadeDeletes(currentUser.Id, address.Id);

            // Delete the address
            _context.Addresses.Remove(address);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Address deleted successfully";

            return RedirectToPage("./Index");
        }

        private async Task HandleCascadeDeletes(string userId, int addressId)
        {
            // Set AddressId to null on entities that should be unlinked (requirements 5-14, 16-18, 22-24)
            await SetAddressIdToNull<GeneralItem>(userId, addressId);
            await SetAddressIdToNull<DrivingLicence>(userId, addressId);
            await SetAddressIdToNull<GpPractice>(userId, addressId);
            await SetAddressIdToNull<Dentist>(userId, addressId);
            await SetAddressIdToNull<LifeInsurancePolicy>(userId, addressId);
            await SetAddressIdToNull<Vehicle>(userId, addressId);
            await SetAddressIdToNull<CurrentAccount>(userId, addressId);
            await SetAddressIdToNull<CreditCard>(userId, addressId);
            await SetAddressIdToNull<SavingsAccount>(userId, addressId);
            await SetAddressIdToNull<Loan>(userId, addressId);
            await SetAddressIdToNull<Pension>(userId, addressId);
            await SetAddressIdToNull<TravelInsurancePolicy>(userId, addressId);
            await SetAddressIdToNull<GadgetInsurancePolicy>(userId, addressId);
            await SetAddressIdToNull<VehicleInsurancePolicy>(userId, addressId);
            await SetAddressIdToNull<VehicleBreakdownPolicy>(userId, addressId);
            await SetAddressIdToNull<VehicleFinanceAgreement>(userId, addressId);

            // Delete entities that should be removed (requirements 15, 19-21)
            await DeleteLinkedEntities<Mortgage>(userId, addressId);
            await DeleteLinkedEntities<UtilityBill>(userId, addressId);
            await DeleteLinkedEntities<Tenancy>(userId, addressId);
            await DeleteLinkedEntities<AddressInsurancePolicy>(userId, addressId);
        }

        private async Task SetAddressIdToNull<T>(string userId, int addressId) where T : class
        {
            var entityType = typeof(T);
            var entities = await _context.Set<T>()
                .Where(e => EF.Property<string>(e, "UserId") == userId && EF.Property<int?>(e, "AddressId") == addressId)
                .ToListAsync();

            foreach (var entity in entities)
            {
                var addressIdProperty = entityType.GetProperty("AddressId");
                if (addressIdProperty != null && addressIdProperty.CanWrite)
                {
                    addressIdProperty.SetValue(entity, null);
                }
            }
        }

        private async Task DeleteLinkedEntities<T>(string userId, int addressId) where T : class
        {
            var entities = await _context.Set<T>()
                .Where(e => EF.Property<string>(e, "UserId") == userId && EF.Property<int?>(e, "AddressId") == addressId)
                .ToListAsync();

            _context.Set<T>().RemoveRange(entities);
        }
    }
}
