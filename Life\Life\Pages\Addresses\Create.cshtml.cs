using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Pages.Addresses
{
    public class CreateModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public CreateModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        [Required]
        [StringLength(20)]
        [Display(Name = "House/Flat Number")]
        public string HouseFlatNumber { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(100)]
        [Display(Name = "Street Line 1")]
        public string StreetLineOne { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(100)]
        [Display(Name = "Street Line 2")]
        public string? StreetLineTwo { get; set; }

        [BindProperty]
        [Required]
        [StringLength(100)]
        public string City { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(20)]
        public string Postcode { get; set; } = string.Empty;

        [BindProperty]
        [Display(Name = "Move In Date")]
        public DateOnly? MoveInDate { get; set; }

        [BindProperty]
        [Display(Name = "Move Out Date")]
        public DateOnly? MoveOutDate { get; set; }

        [BindProperty]
        [Display(Name = "Purchase Date")]
        public DateOnly? PurchaseDate { get; set; }

        [BindProperty]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "Purchase Price")]
        public decimal? PurchasePrice { get; set; }

        [BindProperty]
        [Display(Name = "Sold Date")]
        public DateOnly? SoldDate { get; set; }

        [BindProperty]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "Sold Price")]
        public decimal? SoldPrice { get; set; }

        [BindProperty]
        [StringLength(2000)]
        public string? Notes { get; set; }

        public IActionResult OnGet()
        {
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate date logic
            if (MoveOutDate.HasValue && MoveInDate.HasValue && MoveOutDate <= MoveInDate)
            {
                ModelState.AddModelError(nameof(MoveOutDate), "Move out date must be after move in date");
            }

            if (SoldDate.HasValue && PurchaseDate.HasValue && SoldDate <= PurchaseDate)
            {
                ModelState.AddModelError(nameof(SoldDate), "Sold date must be after purchase date");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Create the address
            var address = new Address
            {
                HouseFlatNumber = HouseFlatNumber,
                StreetLineOne = StreetLineOne,
                StreetLineTwo = StreetLineTwo,
                City = City,
                Postcode = Postcode,
                MoveInDate = MoveInDate,
                MoveOutDate = MoveOutDate,
                PurchaseDate = PurchaseDate,
                PurchasePrice = PurchasePrice,
                SoldDate = SoldDate,
                SoldPrice = SoldPrice,
                Notes = Notes,
                User = currentUser,
                UserId = currentUser.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Addresses.Add(address);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Address created successfully";

            return RedirectToPage("./Index");
        }
    }
}
