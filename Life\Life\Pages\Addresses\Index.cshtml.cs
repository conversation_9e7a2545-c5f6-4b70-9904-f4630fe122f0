using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Addresses
{
    public class IndexModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public IndexModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public IList<Address> Addresses { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();
            
            // Get addresses ordered by MoveInDate desc, then alphabetically by address
            Addresses = await _context.Addresses
                .AsNoTracking()
                .Where(a => a.UserId == currentUser.Id)
                .OrderByDescending(a => a.MoveInDate.HasValue ? a.MoveInDate.Value : DateOnly.MinValue)
                .ThenBy(a => a.HouseFlatNumber)
                .ThenBy(a => a.StreetLineOne)
                .ThenBy(a => a.City)
                .ToListAsync();

            return Page();
        }
    }
}
