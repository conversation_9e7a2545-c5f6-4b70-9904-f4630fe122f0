@page
@model Life.Pages.EyeTests.DetailsModel
@{
    ViewData["Title"] = "Eye Test Details";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2 class="mb-0">Eye Test Details</h2>
                    <a asp-page="./Index" class="btn btn-sm btn-outline-primary title-button">
                        <i class="bi bi-list"></i>
                    </a>
                </div>
                <div class="card-body">

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                            <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                                @TempData["SuccessMessage"]
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        </div>
                    }

                    <div class="row mt-3">
                        <div class="col-md-8">
                            <h4 class="card-title">@Model.Provider</h4>
                            
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Test Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.TestDate.ToString("dd/MM/yyyy")
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Next Test Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.NextTestDate.HasValue)
                                    {
                                        <span class="@(Model.NextTestDate < DateOnly.FromDateTime(DateTime.UtcNow) 
                                            ? "text-danger" 
                                            : Model.NextTestDate == DateOnly.FromDateTime(DateTime.UtcNow) 
                                                ? "text-danger" 
                                                : Model.NextTestDate < DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(6)) 
                                                    ? "text-warning" 
                                                    : "text-success")">
                                            @Model.NextTestDate.Value.ToString("dd/MM/yyyy")
                                            @if (Model.NextTestDate < DateOnly.FromDateTime(DateTime.UtcNow))
                                            {
                                                <span class="badge bg-danger ms-2">Overdue</span>
                                            }
                                            else if (Model.NextTestDate == DateOnly.FromDateTime(DateTime.UtcNow))
                                            {
                                                <span class="badge bg-danger ms-2">Due Today</span>
                                            }
                                            else if (Model.NextTestDate < DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(6)))
                                            {
                                                <span class="badge bg-warning ms-2">Due Soon</span>
                                            }
                                        </span>
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Right Eye:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.RightSPH.HasValue || Model.RightCYL.HasValue || Model.RightAXIS.HasValue)
                                    {
                                        @:SPH: @(Model.RightSPH?.ToString("F2") ?? "N/A"), 
                                        @:CYL: @(Model.RightCYL?.ToString("F2") ?? "N/A"), 
                                        @:AXIS: @(Model.RightAXIS?.ToString("F0") ?? "N/A")
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Left Eye:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.LeftSPH.HasValue || Model.LeftCYL.HasValue || Model.LeftAXIS.HasValue)
                                    {
                                        @:SPH: @(Model.LeftSPH?.ToString("F2") ?? "N/A"), 
                                        @:CYL: @(Model.LeftCYL?.ToString("F2") ?? "N/A"), 
                                        @:AXIS: @(Model.LeftAXIS?.ToString("F0") ?? "N/A")
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Cost:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.Cost.HasValue)
                                    {
                                        @:&#163;@Model.Cost.Value.ToString("F2")
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            @if (!string.IsNullOrWhiteSpace(Model.Notes))
                            {
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <strong>Notes:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <div style="white-space: pre-wrap;">@Model.Notes</div>
                                    </div>
                                </div>
                            }

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Created:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Last Updated:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.UpdatedAt.ToString("dd/MM/yyyy HH:mm")
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="bi bi-bell"></i> Reminder Status
                                    </h6>
                                    @if (Model.HasLinkedReminder)
                                    {
                                        <p class="text-success">
                                            <i class="bi bi-check-circle"></i> 
                                            Automatic reminder is set
                                        </p>
                                    }
                                    else
                                    {
                                        <p class="text-warning">
                                            <i class="bi bi-exclamation-triangle"></i> 
                                            No automatic reminder found
                                        </p>
                                    }
                                </div>
                            </div>

                            @if (Model.NextTestDate.HasValue)
                            {
                                <div class="card bg-light mt-3">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="bi bi-calendar"></i> Time Until Next Test
                                        </h6>
                                        @{
                                            var today = DateOnly.FromDateTime(DateTime.UtcNow);
                                            var daysUntilNextTest = (Model.NextTestDate.Value.DayNumber - today.DayNumber);
                                        }
                                        @if (daysUntilNextTest < 0)
                                        {
                                            <p class="text-danger">
                                                Overdue by @Math.Abs(daysUntilNextTest) days
                                            </p>
                                        }
                                        else if (daysUntilNextTest == 0)
                                        {
                                            <p class="text-danger">
                                                Due today!
                                            </p>
                                        }
                                        else
                                        {
                                            <p class="@(daysUntilNextTest < 180 ? "text-warning" : "text-success")">
                                                @if (daysUntilNextTest == 1)
                                                {
                                                    @:1 day remaining
                                                }
                                                else
                                                {
                                                    @daysUntilNextTest @:days remaining
                                                }
                                            </p>
                                        }
                                    </div>
                                </div>
                            }

                            <div class="d-flex justify-content-between mt-3">
                                <a asp-page="./Edit" asp-route-id="@Model.EyeTestId" class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a asp-page="./Delete" asp-route-id="@Model.EyeTestId" class="btn btn-sm btn-outline-danger">
                                    <i class="bi bi-trash"></i> Delete
                                </a>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
