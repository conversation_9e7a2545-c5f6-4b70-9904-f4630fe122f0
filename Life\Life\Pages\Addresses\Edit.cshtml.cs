using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Pages.Addresses
{
    public class EditModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public EditModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int AddressId { get; set; }

        [BindProperty]
        [Required]
        [StringLength(20)]
        [Display(Name = "House/Flat Number")]
        public string HouseFlatNumber { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(100)]
        [Display(Name = "Street Line 1")]
        public string StreetLineOne { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(100)]
        [Display(Name = "Street Line 2")]
        public string? StreetLineTwo { get; set; }

        [BindProperty]
        [Required]
        [StringLength(100)]
        public string City { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(20)]
        public string Postcode { get; set; } = string.Empty;

        [BindProperty]
        [Display(Name = "Move In Date")]
        public DateOnly? MoveInDate { get; set; }

        [BindProperty]
        [Display(Name = "Move Out Date")]
        public DateOnly? MoveOutDate { get; set; }

        [BindProperty]
        [Display(Name = "Purchase Date")]
        public DateOnly? PurchaseDate { get; set; }

        [BindProperty]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "Purchase Price")]
        public decimal? PurchasePrice { get; set; }

        [BindProperty]
        [Display(Name = "Sold Date")]
        public DateOnly? SoldDate { get; set; }

        [BindProperty]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "Sold Price")]
        public decimal? SoldPrice { get; set; }

        [BindProperty]
        [StringLength(2000)]
        public string? Notes { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var address = await _context.Addresses
                .Where(a => a.Id == id && a.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (address == null)
            {
                return NotFound();
            }

            AddressId = address.Id;
            HouseFlatNumber = address.HouseFlatNumber;
            StreetLineOne = address.StreetLineOne;
            StreetLineTwo = address.StreetLineTwo;
            City = address.City;
            Postcode = address.Postcode;
            MoveInDate = address.MoveInDate;
            MoveOutDate = address.MoveOutDate;
            PurchaseDate = address.PurchaseDate;
            PurchasePrice = address.PurchasePrice;
            SoldDate = address.SoldDate;
            SoldPrice = address.SoldPrice;
            Notes = address.Notes;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate date logic
            if (MoveOutDate.HasValue && MoveInDate.HasValue && MoveOutDate <= MoveInDate)
            {
                ModelState.AddModelError(nameof(MoveOutDate), "Move out date must be after move in date");
            }

            if (SoldDate.HasValue && PurchaseDate.HasValue && SoldDate <= PurchaseDate)
            {
                ModelState.AddModelError(nameof(SoldDate), "Sold date must be after purchase date");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            var address = await _context.Addresses
                .Where(a => a.Id == AddressId && a.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (address == null)
            {
                return NotFound();
            }

            // Update the address properties
            address.HouseFlatNumber = HouseFlatNumber;
            address.StreetLineOne = StreetLineOne;
            address.StreetLineTwo = StreetLineTwo;
            address.City = City;
            address.Postcode = Postcode;
            address.MoveInDate = MoveInDate;
            address.MoveOutDate = MoveOutDate;
            address.PurchaseDate = PurchaseDate;
            address.PurchasePrice = PurchasePrice;
            address.SoldDate = SoldDate;
            address.SoldPrice = SoldPrice;
            address.Notes = Notes;
            address.UpdatedAt = DateTime.UtcNow;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!AddressExists(AddressId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            TempData["SuccessMessage"] = "Address updated successfully";

            return RedirectToPage("./Details", new { id = AddressId });
        }

        private bool AddressExists(int id)
        {
            return _context.Addresses.Any(e => e.Id == id);
        }
    }
}
