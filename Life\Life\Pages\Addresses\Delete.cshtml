@page
@model Life.Pages.Addresses.DeleteModel
@{
    ViewData["Title"] = "Delete Address";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Delete Address</h2>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <h4 class="alert-heading">
                            <i class="bi bi-exclamation-triangle"></i> Warning!
                        </h4>
                        <p>
                            You are about to delete the address: <strong>@Model.AddressDisplay</strong>
                        </p>
                        <hr>
                        <p class="mb-0">
                            <strong>This action will:</strong>
                        </p>
                        <ul class="mt-2">
                            <li>Permanently delete this address record</li>
                            <li>Remove the address link from most related entities (they will remain but without an address)</li>
                            <li><strong>Permanently delete</strong> any Mortgages, Utility Bills, Tenancies, and Home Insurance Policies linked to this address</li>
                        </ul>
                        <p class="mb-0 mt-3">
                            <strong>This action cannot be undone.</strong>
                        </p>
                    </div>

                    <form method="post">
                        <input type="hidden" asp-for="AddressId" />
                        <div class="row">
                            <div class="col-sm-12">
                                <button type="submit" class="btn btn-danger">
                                    <i class="bi bi-trash"></i> Yes, Delete Address
                                </button>
                                <a asp-page="./Details" asp-route-id="@Model.AddressId" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
